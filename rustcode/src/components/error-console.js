import { createIcon } from '../icons/icon-library.js';

export class ErrorConsole {
    constructor(container) {
        this.container = container;
        this.errors = [];
        this.isVisible = false;
        this.currentFilter = 'all'; // all, error, warning, info
        this.maxErrors = 1000; // Prevent memory issues
        
        this.init();
        this.setupErrorCapturing();
    }

    init() {
        this.createUI();
        this.attachEventListeners();
        this.setupKeyboardShortcuts();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="error-console ${this.isVisible ? 'visible' : 'hidden'}">
                <!-- Header -->
                <div class="error-console-header">
                    <div class="error-console-title">
                        ${createIcon('bug', 18)}
                        <span>Error Console</span>
                        <span class="error-count" id="error-count">0 errors</span>
                    </div>
                    <div class="error-console-controls">
                        <!-- Filter Buttons -->
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all" title="Show All">
                                All
                            </button>
                            <button class="filter-btn" data-filter="error" title="Show Errors">
                                ${createIcon('error', 14)}
                                <span class="filter-count" id="error-filter-count">0</span>
                            </button>
                            <button class="filter-btn" data-filter="warning" title="Show Warnings">
                                ${createIcon('warning', 14)}
                                <span class="filter-count" id="warning-filter-count">0</span>
                            </button>
                            <button class="filter-btn" data-filter="info" title="Show Info">
                                ${createIcon('info', 14)}
                                <span class="filter-count" id="info-filter-count">0</span>
                            </button>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button id="copy-all-errors" class="action-btn" title="Copy All Errors">
                                ${createIcon('copy', 16)}
                            </button>
                            <button id="clear-errors" class="action-btn" title="Clear All Errors">
                                ${createIcon('trash', 16)}
                            </button>
                            <button id="toggle-console" class="action-btn" title="Toggle Console">
                                ${createIcon('chevronDown', 16)}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="error-console-content">
                    <div class="error-list" id="error-list">
                        <div class="no-errors-message">
                            <div class="no-errors-icon">
                                ${createIcon('check', 32)}
                            </div>
                            <p>No errors detected</p>
                            <small>The error console will display runtime errors, syntax issues, and diagnostic messages.</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    attachEventListeners() {
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.currentTarget.dataset.filter;
                this.setFilter(filter);
            });
        });

        // Action buttons
        document.getElementById('copy-all-errors').addEventListener('click', () => {
            this.copyAllErrors();
        });

        document.getElementById('clear-errors').addEventListener('click', () => {
            this.clearErrors();
        });

        document.getElementById('toggle-console').addEventListener('click', () => {
            this.toggle();
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+E to toggle error console
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                this.toggle();
            }
            // Ctrl+Shift+C to copy all errors
            if (e.ctrlKey && e.shiftKey && e.key === 'C' && this.isVisible) {
                e.preventDefault();
                this.copyAllErrors();
            }
        });
    }

    setupErrorCapturing() {
        // Capture JavaScript runtime errors
        window.addEventListener('error', (event) => {
            this.addError({
                type: 'runtime',
                severity: 'error',
                message: event.message,
                file: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error?.stack,
                timestamp: new Date()
            });
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.addError({
                type: 'promise',
                severity: 'error',
                message: `Unhandled Promise Rejection: ${event.reason}`,
                stack: event.reason?.stack,
                timestamp: new Date()
            });
        });

        // Override console.error to capture console errors
        this.originalConsoleError = console.error;
        console.error = (...args) => {
            this.originalConsoleError.apply(console, args);
            this.addError({
                type: 'console',
                severity: 'error',
                message: args.join(' '),
                timestamp: new Date()
            });
        };

        // Override console.warn to capture warnings
        this.originalConsoleWarn = console.warn;
        console.warn = (...args) => {
            this.originalConsoleWarn.apply(console, args);
            this.addError({
                type: 'console',
                severity: 'warning',
                message: args.join(' '),
                timestamp: new Date()
            });
        };
    }

    addError(errorData) {
        // Prevent duplicate errors (same message within 1 second)
        const now = Date.now();
        const isDuplicate = this.errors.some(error => 
            error.message === errorData.message && 
            (now - error.timestamp.getTime()) < 1000
        );

        if (isDuplicate) return;

        // Add unique ID
        errorData.id = Date.now() + Math.random();
        
        // Add to beginning of array (newest first)
        this.errors.unshift(errorData);

        // Limit array size
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(0, this.maxErrors);
        }

        this.updateDisplay();
        this.updateCounts();

        // Auto-show console on new errors (if not already visible)
        if (!this.isVisible && errorData.severity === 'error') {
            this.show();
        }
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });

        this.updateDisplay();
    }

    updateDisplay() {
        const errorList = document.getElementById('error-list');
        const filteredErrors = this.getFilteredErrors();

        if (filteredErrors.length === 0) {
            errorList.innerHTML = `
                <div class="no-errors-message">
                    <div class="no-errors-icon">
                        ${createIcon('check', 32)}
                    </div>
                    <p>${this.currentFilter === 'all' ? 'No errors detected' : `No ${this.currentFilter}s found`}</p>
                    <small>The error console will display runtime errors, syntax issues, and diagnostic messages.</small>
                </div>
            `;
            return;
        }

        errorList.innerHTML = filteredErrors.map(error => this.createErrorElement(error)).join('');
    }

    getFilteredErrors() {
        if (this.currentFilter === 'all') {
            return this.errors;
        }
        return this.errors.filter(error => error.severity === this.currentFilter);
    }

    createErrorElement(error) {
        const timeStr = error.timestamp.toLocaleTimeString();
        const fileInfo = error.file ? `${error.file}${error.line ? `:${error.line}` : ''}` : '';
        
        return `
            <div class="error-item ${error.severity}" data-error-id="${error.id}">
                <div class="error-header">
                    <div class="error-icon">
                        ${this.getErrorIcon(error.severity)}
                    </div>
                    <div class="error-meta">
                        <span class="error-type">${error.type.toUpperCase()}</span>
                        <span class="error-time">${timeStr}</span>
                        ${fileInfo ? `<span class="error-file">${fileInfo}</span>` : ''}
                    </div>
                    <button class="copy-error-btn" onclick="errorConsole.copyError('${error.id}')" title="Copy Error">
                        ${createIcon('copy', 14)}
                    </button>
                </div>
                <div class="error-message">
                    ${this.formatErrorMessage(error.message)}
                </div>
                ${error.stack ? `
                    <div class="error-stack">
                        <button class="toggle-stack" onclick="this.nextElementSibling.classList.toggle('expanded')">
                            ${createIcon('chevronRight', 12)} Stack Trace
                        </button>
                        <pre class="stack-trace"><code>${this.escapeHtml(error.stack)}</code></pre>
                    </div>
                ` : ''}
            </div>
        `;
    }

    getErrorIcon(severity) {
        switch (severity) {
            case 'error': return createIcon('error', 16);
            case 'warning': return createIcon('warning', 16);
            case 'info': return createIcon('info', 16);
            default: return createIcon('bug', 16);
        }
    }

    formatErrorMessage(message) {
        // Basic syntax highlighting for error messages
        return this.escapeHtml(message)
            .replace(/(\w+Error:)/g, '<span class="error-type-highlight">$1</span>')
            .replace(/('.*?'|".*?")/g, '<span class="string-highlight">$1</span>')
            .replace(/(\d+)/g, '<span class="number-highlight">$1</span>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updateCounts() {
        const errorCount = this.errors.filter(e => e.severity === 'error').length;
        const warningCount = this.errors.filter(e => e.severity === 'warning').length;
        const infoCount = this.errors.filter(e => e.severity === 'info').length;
        const totalCount = this.errors.length;

        document.getElementById('error-count').textContent = 
            `${totalCount} ${totalCount === 1 ? 'error' : 'errors'}`;
        document.getElementById('error-filter-count').textContent = errorCount;
        document.getElementById('warning-filter-count').textContent = warningCount;
        document.getElementById('info-filter-count').textContent = infoCount;
    }

    copyError(errorId) {
        const error = this.errors.find(e => e.id == errorId);
        if (!error) return;

        const errorText = this.formatErrorForCopy(error);
        this.copyToClipboard(errorText);
        this.showCopyNotification('Error copied to clipboard');
    }

    copyAllErrors() {
        if (this.errors.length === 0) {
            this.showCopyNotification('No errors to copy');
            return;
        }

        const filteredErrors = this.getFilteredErrors();
        const allErrorsText = filteredErrors.map(error => this.formatErrorForCopy(error)).join('\n\n---\n\n');
        
        this.copyToClipboard(allErrorsText);
        this.showCopyNotification(`${filteredErrors.length} errors copied to clipboard`);
    }

    formatErrorForCopy(error) {
        const timeStr = error.timestamp.toLocaleString();
        const fileInfo = error.file ? `${error.file}${error.line ? `:${error.line}` : ''}` : 'Unknown file';
        
        let text = `[${error.severity.toUpperCase()}] ${error.type.toUpperCase()} - ${timeStr}\n`;
        text += `File: ${fileInfo}\n`;
        text += `Message: ${error.message}\n`;
        
        if (error.stack) {
            text += `Stack Trace:\n${error.stack}`;
        }
        
        return text;
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    showCopyNotification(message) {
        // Create a temporary notification
        const notification = document.createElement('div');
        notification.className = 'copy-notification';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 2000);
    }

    clearErrors() {
        this.errors = [];
        this.updateDisplay();
        this.updateCounts();
    }

    show() {
        this.isVisible = true;
        this.container.querySelector('.error-console').classList.remove('hidden');
        this.container.querySelector('.error-console').classList.add('visible');
        
        // Update toggle button icon
        const toggleBtn = document.getElementById('toggle-console');
        if (toggleBtn) {
            toggleBtn.innerHTML = createIcon('chevronDown', 16);
        }
    }

    hide() {
        this.isVisible = false;
        this.container.querySelector('.error-console').classList.remove('visible');
        this.container.querySelector('.error-console').classList.add('hidden');
        
        // Update toggle button icon
        const toggleBtn = document.getElementById('toggle-console');
        if (toggleBtn) {
            toggleBtn.innerHTML = createIcon('chevronUp', 16);
        }
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // Public API for other components to add errors
    logError(message, file = null, line = null) {
        this.addError({
            type: 'application',
            severity: 'error',
            message,
            file,
            line,
            timestamp: new Date()
        });
    }

    logWarning(message, file = null, line = null) {
        this.addError({
            type: 'application',
            severity: 'warning',
            message,
            file,
            line,
            timestamp: new Date()
        });
    }

    logInfo(message, file = null, line = null) {
        this.addError({
            type: 'application',
            severity: 'info',
            message,
            file,
            line,
            timestamp: new Date()
        });
    }

    destroy() {
        // Restore original console methods
        if (this.originalConsoleError) {
            console.error = this.originalConsoleError;
        }
        if (this.originalConsoleWarn) {
            console.warn = this.originalConsoleWarn;
        }
    }
}

// Make it globally accessible for onclick handlers
window.errorConsole = null;
