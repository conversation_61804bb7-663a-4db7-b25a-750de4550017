// Monaco Editor Worker Configuration for Vite
// This file provides a fallback worker implementation for Monaco Editor

self.MonacoEnvironment = {
    baseUrl: '/',
    getWorkerUrl: function (moduleId, label) {
        // Return a data URL that creates a minimal worker
        return `data:text/javascript;charset=utf-8,${encodeURIComponent(`
            self.MonacoEnvironment = {
                baseUrl: '/'
            };
            importScripts('${new URL('/node_modules/monaco-editor/min/vs/base/worker/workerMain.js', self.location)}');
        `)}`;
    }
};

// Minimal worker implementation
class MockWorker {
    constructor() {
        this.onmessage = null;
        this.onerror = null;
    }

    postMessage(data) {
        // Mock implementation - just echo back
        setTimeout(() => {
            if (this.onmessage) {
                this.onmessage({ data: { id: data.id, result: null } });
            }
        }, 0);
    }

    terminate() {
        // Mock implementation
    }

    addEventListener(type, listener) {
        if (type === 'message') {
            this.onmessage = listener;
        } else if (type === 'error') {
            this.onerror = listener;
        }
    }

    removeEventListener(type, listener) {
        if (type === 'message' && this.onmessage === listener) {
            this.onmessage = null;
        } else if (type === 'error' && this.onerror === listener) {
            this.onerror = null;
        }
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MockWorker };
} else if (typeof window !== 'undefined') {
    window.MockWorker = MockWorker;
}
