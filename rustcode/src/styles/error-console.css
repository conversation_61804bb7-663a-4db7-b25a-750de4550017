/* Error Console <PERSON> */

:root {
    --error-console-bg: #1e1e1e;
    --error-console-border: #3e3e42;
    --error-console-header-bg: #252526;
    --error-console-text: #cccccc;
    --error-console-text-secondary: #9d9d9d;
    --error-console-text-muted: #6f6f6f;
    --error-console-error: #f48771;
    --error-console-warning: #f9c74f;
    --error-console-info: #75beff;
    --error-console-success: #89d185;
    --error-console-hover: #2d2d30;
    --error-console-active: #37373d;
}

.error-console {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 300px;
    background: var(--error-console-bg);
    border-top: 1px solid var(--error-console-border);
    color: var(--error-console-text);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.error-console.hidden {
    transform: translateY(100%);
    opacity: 0;
    pointer-events: none;
}

.error-console.visible {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
}

/* Header */
.error-console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--error-console-header-bg);
    border-bottom: 1px solid var(--error-console-border);
    min-height: 40px;
}

.error-console-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
}

.error-count {
    background: var(--error-console-hover);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    color: var(--error-console-text-secondary);
}

.error-console-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    color: var(--error-console-text-secondary);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: var(--error-console-hover);
    color: var(--error-console-text);
}

.filter-btn.active {
    background: var(--error-console-active);
    border-color: var(--error-console-border);
    color: var(--error-console-text);
}

.filter-count {
    background: var(--error-console-hover);
    padding: 1px 4px;
    border-radius: 8px;
    font-size: 10px;
    min-width: 16px;
    text-align: center;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    color: var(--error-console-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--error-console-hover);
    border-color: var(--error-console-border);
    color: var(--error-console-text);
}

/* Content */
.error-console-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.error-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

/* No Errors Message */
.no-errors-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--error-console-text-secondary);
}

.no-errors-icon {
    color: var(--error-console-success);
    margin-bottom: 12px;
    opacity: 0.6;
}

.no-errors-message p {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 500;
}

.no-errors-message small {
    font-size: 12px;
    color: var(--error-console-text-muted);
    max-width: 300px;
    line-height: 1.4;
}

/* Error Items */
.error-item {
    margin-bottom: 8px;
    border: 1px solid var(--error-console-border);
    border-radius: 6px;
    background: var(--error-console-header-bg);
    overflow: hidden;
}

.error-item.error {
    border-left: 4px solid var(--error-console-error);
}

.error-item.warning {
    border-left: 4px solid var(--error-console-warning);
}

.error-item.info {
    border-left: 4px solid var(--error-console-info);
}

.error-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: var(--error-console-hover);
    border-bottom: 1px solid var(--error-console-border);
}

.error-icon {
    margin-right: 8px;
    flex-shrink: 0;
}

.error-item.error .error-icon {
    color: var(--error-console-error);
}

.error-item.warning .error-icon {
    color: var(--error-console-warning);
}

.error-item.info .error-icon {
    color: var(--error-console-info);
}

.error-meta {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 11px;
    color: var(--error-console-text-secondary);
}

.error-type {
    font-weight: 600;
    color: var(--error-console-text);
}

.error-time {
    color: var(--error-console-text-muted);
}

.error-file {
    color: var(--error-console-info);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.copy-error-btn {
    background: transparent;
    border: none;
    color: var(--error-console-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.copy-error-btn:hover {
    background: var(--error-console-active);
    color: var(--error-console-text);
}

.error-message {
    padding: 12px;
    font-size: 13px;
    line-height: 1.4;
    color: var(--error-console-text);
    word-break: break-word;
}

/* Syntax Highlighting */
.error-type-highlight {
    color: var(--error-console-error);
    font-weight: 600;
}

.string-highlight {
    color: var(--error-console-warning);
}

.number-highlight {
    color: var(--error-console-info);
}

/* Stack Trace */
.error-stack {
    border-top: 1px solid var(--error-console-border);
    background: var(--error-console-bg);
}

.toggle-stack {
    display: flex;
    align-items: center;
    gap: 4px;
    width: 100%;
    padding: 8px 12px;
    background: transparent;
    border: none;
    color: var(--error-console-text-secondary);
    font-size: 12px;
    cursor: pointer;
    text-align: left;
    transition: all 0.2s ease;
}

.toggle-stack:hover {
    background: var(--error-console-hover);
    color: var(--error-console-text);
}

.stack-trace {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    margin: 0;
    padding: 0;
    background: var(--error-console-bg);
    border-top: 1px solid var(--error-console-border);
}

.stack-trace.expanded {
    max-height: 200px;
    overflow-y: auto;
    padding: 12px;
}

.stack-trace code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    color: var(--error-console-text-secondary);
    white-space: pre-wrap;
    word-break: break-all;
}

/* Copy Notification */
.copy-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--error-console-success);
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.copy-notification.show {
    transform: translateX(0);
    opacity: 1;
}

/* Scrollbar Styling */
.error-list::-webkit-scrollbar,
.stack-trace::-webkit-scrollbar {
    width: 8px;
}

.error-list::-webkit-scrollbar-track,
.stack-trace::-webkit-scrollbar-track {
    background: var(--error-console-bg);
}

.error-list::-webkit-scrollbar-thumb,
.stack-trace::-webkit-scrollbar-thumb {
    background: var(--error-console-border);
    border-radius: 4px;
}

.error-list::-webkit-scrollbar-thumb:hover,
.stack-trace::-webkit-scrollbar-thumb:hover {
    background: var(--error-console-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
    .error-console {
        height: 250px;
    }
    
    .error-console-header {
        padding: 6px 12px;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .error-console-controls {
        gap: 8px;
    }
    
    .filter-buttons {
        gap: 2px;
    }
    
    .filter-btn {
        padding: 3px 6px;
        font-size: 10px;
    }
    
    .action-btn {
        width: 24px;
        height: 24px;
    }
    
    .error-message {
        font-size: 12px;
        padding: 10px;
    }
}

/* Animation for error items */
.error-item {
    animation: slideInError 0.3s ease-out;
}

@keyframes slideInError {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
