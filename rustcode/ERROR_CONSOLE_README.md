# RustCode Universal Error Console

## Overview

The Universal Error Console is a comprehensive debugging tool for RustCode that aggregates and displays all IDE errors, warnings, and diagnostic messages in a centralized, easily accessible interface.

## Features

### 🔍 **Comprehensive Error Collection**
- **JavaScript Runtime Errors**: Captures all JavaScript errors from the editor and components
- **Syntax Errors**: Displays syntax errors from code files
- **Console Errors**: Intercepts and displays console.error() and console.warn() output
- **Promise Rejections**: Catches unhandled promise rejections
- **Application Errors**: Logs custom application errors from RustCode components

### 🎨 **User-Friendly Interface**
- **Bottom Panel Design**: Slides up from the bottom like VS Code's Problems panel
- **Error Categorization**: Filters by error type (Error, Warning, Info)
- **Timestamps**: Shows when each error occurred
- **File Information**: Displays file names and line numbers when available
- **Syntax Highlighting**: Color-coded error messages and stack traces
- **Expandable Stack Traces**: Click to view full stack traces

### 📋 **Copy-Paste Functionality**
- **Copy Individual Errors**: Each error has a copy button
- **Copy All Errors**: Bulk copy all visible errors
- **Formatted Output**: Clean, readable format for sharing with developers
- **Context Preservation**: Includes file names, line numbers, and timestamps

### ⌨️ **Keyboard Shortcuts**
- **Ctrl+Shift+E**: Toggle error console visibility
- **Ctrl+Shift+C**: Copy all errors (when console is visible)

### 🎛️ **Advanced Features**
- **Real-time Updates**: Automatically captures new errors as they occur
- **Error Filtering**: Filter by severity (All, Error, Warning, Info)
- **Error Counts**: Shows count of each error type
- **Auto-show**: Automatically shows console when new errors occur
- **Duplicate Prevention**: Prevents duplicate errors within 1 second
- **Memory Management**: Limits to 1000 errors to prevent memory issues

## Usage

### Opening the Error Console

1. **Menu Bar**: Click "Errors" in the menu bar
2. **Keyboard Shortcut**: Press `Ctrl+Shift+E`
3. **Programmatically**: Call `window.rustCodeApp.toggleErrorConsole()`

### Testing the Error Console

You can test the error console functionality by running this in the browser console:

```javascript
// Test the error console with sample errors
testErrorConsole();
```

This will generate various types of test errors to demonstrate the console's capabilities.

### Filtering Errors

Use the filter buttons in the header to show specific types of errors:
- **All**: Shows all errors, warnings, and info messages
- **Error**: Shows only error-level messages
- **Warning**: Shows only warning-level messages  
- **Info**: Shows only informational messages

### Copying Errors

1. **Individual Error**: Click the copy button (📋) next to any error
2. **All Errors**: Click the copy button in the header to copy all visible errors
3. **Keyboard**: Press `Ctrl+Shift+C` when the console is visible

### Clearing Errors

Click the trash button (🗑️) in the header to clear all errors from the console.

## Integration with RustCode Components

### Logging Custom Errors

Components can log errors to the console using these methods:

```javascript
// Log an error
window.errorConsole.logError('Error message', 'filename.js', 123);

// Log a warning
window.errorConsole.logWarning('Warning message', 'filename.js', 456);

// Log info
window.errorConsole.logInfo('Info message', 'filename.js', 789);
```

### Automatic Error Capture

The error console automatically captures:
- `window.addEventListener('error')` - JavaScript runtime errors
- `window.addEventListener('unhandledrejection')` - Promise rejections
- `console.error()` - Console error messages
- `console.warn()` - Console warning messages

## Error Format

When copying errors, they are formatted as:

```
[ERROR] RUNTIME - 12/25/2023, 10:30:45 AM
File: main.js:123
Message: Cannot read property 'value' of null
Stack Trace:
    at EditorManager.getValue (main.js:123:45)
    at RustCodeApp.onEditorChange (main.js:456:78)
    ...
```

## Styling and Theming

The error console uses RustCode's dark theme with:
- **Error Color**: Red (#f48771)
- **Warning Color**: Yellow (#f9c74f)  
- **Info Color**: Blue (#75beff)
- **Success Color**: Green (#89d185)

## Performance Considerations

- **Memory Limit**: Maximum 1000 errors stored
- **Duplicate Prevention**: Same error within 1 second is ignored
- **Efficient Rendering**: Only visible errors are rendered
- **Lazy Loading**: Stack traces are collapsed by default

## Browser Compatibility

The error console works in all modern browsers and includes fallbacks for:
- **Clipboard API**: Falls back to document.execCommand for older browsers
- **Console Interception**: Safely wraps console methods
- **Error Handling**: Graceful degradation if features are unavailable

## Troubleshooting

### Console Not Showing
- Check if `#error-console-container` exists in the DOM
- Verify the CSS file is loaded
- Check browser console for initialization errors

### Errors Not Appearing
- Verify error console is initialized: `window.errorConsole`
- Check if errors are being filtered out
- Test with `testErrorConsole()` function

### Copy Function Not Working
- Check browser permissions for clipboard access
- Try the fallback copy method
- Verify the copy notification appears

## Development

### File Structure
```
rustcode/src/
├── components/
│   └── error-console.js     # Main error console component
├── styles/
│   └── error-console.css    # Error console styling
└── main.js                  # Integration with main app
```

### Key Classes and Methods

**ErrorConsole Class**:
- `addError(errorData)` - Add new error to console
- `setFilter(filter)` - Change error filter
- `copyAllErrors()` - Copy all errors to clipboard
- `clearErrors()` - Clear all errors
- `toggle()` - Show/hide console

### Contributing

When adding new error types or features:
1. Update the error data structure in `addError()`
2. Add appropriate styling in `error-console.css`
3. Update this documentation
4. Test with various error scenarios

## License

Part of the RustCode project. See main project license for details.
